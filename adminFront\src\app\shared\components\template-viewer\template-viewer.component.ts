import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-template-viewer',
  templateUrl: './template-viewer.component.html',
  styleUrls: ['./template-viewer.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule],
})
export class TemplateViewerComponent {
  @Input() templates: Template[] = [];
  @Input() templateDetails: TemplateDetail[] = [];
  @Input() sharedData: any[] = [];
  @Output() addTemplate = new EventEmitter<Template>();
  @Output() selectTemplate = new EventEmitter<Template>();
  @Output() saveTemplate = new EventEmitter<{ template: Template, details: TemplateDetail[] }>();
  @Output() deleteTemplate = new EventEmitter<number>();

  showCreateTemplate = false;
  newTemplateName = '';
  newTemplateDesc = '';
  selectedTemplate: Template | null = null;

  // 建立模板
  createTemplate() {
    const selected = (this.sharedData || []).filter(x => x.selected);
    if (!this.newTemplateName || selected.length === 0) {
      alert('請輸入模板名稱並選擇資料');
      return;
    }
    const template: Template = {
      TemplateName: this.newTemplateName,
      Description: this.newTemplateDesc
    };
    // details 依據選擇資料組成
    const details: TemplateDetail[] = selected.map(x => ({
      TemplateID: 0, // 新增時由後端補上
      RefID: x.ID || x.CRequirementID || 0,
      ModuleType: x.ModuleType || 'Requirement',
      FieldName: 'CRequirement',
      FieldValue: x.CRequirement
    }));
    this.saveTemplate.emit({ template, details });
    this.showCreateTemplate = false;
    this.newTemplateName = '';
    this.newTemplateDesc = '';
    (this.sharedData || []).forEach(x => x.selected = false);
  }

  // 新增模板
  onAddTemplate() {
    this.addTemplate.emit();
  }

  // 查看模板
  onSelectTemplate(template: Template) {
    this.selectedTemplate = template;
    this.selectTemplate.emit(template);
  }

  // 刪除模板
  onDeleteTemplate(templateID: number) {
    if (confirm('確定刪除此模板？')) {
      this.deleteTemplate.emit(templateID);
    }
  }

  // 關閉模板詳情
  closeTemplateDetail() {
    this.selectedTemplate = null;
  }

  // 取得當前選中模板的詳情
  get currentTemplateDetails(): TemplateDetail[] {
    if (!this.selectedTemplate) {
      return [];
    }
    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);
  }
}

// DB 對應型別
export interface Template {
  TemplateID?: number;
  TemplateName: string;
  Description?: string;
}
export interface TemplateDetail {
  TemplateDetailID?: number;
  TemplateID: number;
  RefID: number; // 關聯主檔ID
  ModuleType: string;
  FieldName: string;
  FieldValue: string;
}
