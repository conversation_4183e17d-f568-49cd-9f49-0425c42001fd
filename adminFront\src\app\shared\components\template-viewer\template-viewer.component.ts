import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-template-viewer',
  templateUrl: './template-viewer.component.html',
  styleUrls: ['./template-viewer.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule],
})
export class TemplateViewerComponent implements OnInit, OnChanges {
  @Input() templates: Template[] = [];
  @Input() templateDetails: TemplateDetail[] = [];
  @Output() addTemplate = new EventEmitter<void>();
  @Output() selectTemplate = new EventEmitter<Template>();
  @Output() deleteTemplate = new EventEmitter<number>();


  selectedTemplate: Template | null = null;

  // 查詢功能
  searchKeyword = '';
  filteredTemplates: Template[] = [];

  ngOnInit() {
    this.updateFilteredTemplates();
  }

  ngOnChanges() {
    this.updateFilteredTemplates();
  }

  // 更新過濾後的模板列表
  updateFilteredTemplates() {
    if (!this.searchKeyword.trim()) {
      this.filteredTemplates = [...this.templates];
    } else {
      const keyword = this.searchKeyword.toLowerCase();
      this.filteredTemplates = this.templates.filter(template =>
        template.TemplateName.toLowerCase().includes(keyword) ||
        (template.Description && template.Description.toLowerCase().includes(keyword))
      );
    }
  }

  // 搜尋模板
  onSearch() {
    this.updateFilteredTemplates();
  }

  // 清除搜尋
  clearSearch() {
    this.searchKeyword = '';
    this.updateFilteredTemplates();
  }



  // 新增模板
  onAddTemplate() {
    this.addTemplate.emit();
  }

  // 查看模板
  onSelectTemplate(template: Template) {
    this.selectedTemplate = template;
    this.selectTemplate.emit(template);
  }

  // 刪除模板
  onDeleteTemplate(templateID: number) {
    if (confirm('確定刪除此模板？')) {
      this.deleteTemplate.emit(templateID);
    }
  }

  // 關閉模板詳情
  closeTemplateDetail() {
    this.selectedTemplate = null;
  }

  // 取得當前選中模板的詳情
  get currentTemplateDetails(): TemplateDetail[] {
    if (!this.selectedTemplate) {
      return [];
    }
    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);
  }

  // TrackBy函數用於優化ngFor性能
  trackByTemplateId(index: number, template: Template): number {
    return template.TemplateID || index;
  }
}

// DB 對應型別
export interface Template {
  TemplateID?: number;
  TemplateName: string;
  Description?: string;
}
export interface TemplateDetail {
  TemplateDetailID?: number;
  TemplateID: number;
  RefID: number; // 關聯主檔ID
  ModuleType: string;
  FieldName: string;
  FieldValue: string;
}
