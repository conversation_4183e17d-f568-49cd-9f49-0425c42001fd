<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>

  <!-- 共用搜尋區域 -->
  <nb-card-body class="bg-white pb-0">
    <div class="col-12">
      <div class="row">
        <div class="form-group col-12 col-md-4" *ngIf="currentTab === 0">
          <label for="buildCase" class="label mr-2">建案</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CBuildCaseID" class="col-9">
            <nb-option *ngFor="let case of buildCaseList" [value]="case.cID">
              {{ case.CBuildCaseName }}
            </nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="requirement" class="label mr-2">工程項目</label>
          <input type="text" nbInput id="requirement" name="requirement" placeholder="工程項目"
            [(ngModel)]="getListRequirementRequest.CRequirement">
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="groupName" class="label mr-2">群組類別</label>
          <input type="text" nbInput id="groupName" name="groupName" placeholder="群組類別"
            [(ngModel)]="getListRequirementRequest.CGroupName">
        </div>
      </div>
      <div class="row">
        <div class="form-group col-12 col-md-4">
          <label for="houseType" class="label mr-2">類型</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CHouseType" class="col-9" multiple>
            <nb-option *ngFor="let type of houseType" [value]="type.value">
              {{ type.label }}
            </nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="status" class="label mr-2">狀態</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CStatus" class="col-9">
            <nb-option [value]="-1">全部</nb-option>
            <nb-option [value]="1">啟用</nb-option>
            <nb-option [value]="0">停用</nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="isShow" class="label mr-2">客變需求顯示</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CIsShow" class="col-9">
            <nb-option [value]="null">全部</nb-option>
            <nb-option [value]="true">是</nb-option>
            <nb-option [value]="false">否</nb-option>
          </nb-select>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6"></div>
        <div class="form-group col-12 col-md-6 text-right">
          <button class="btn btn-secondary mr-2" (click)="resetSearch()"><i class="fas fa-undo mr-1"></i>重置</button>
          <button class="btn btn-info mr-2" (click)="getList()"><i class="fas fa-search mr-1"></i>查詢</button>
          <button class="btn btn-success mr-2" (click)="add(dialog)" *ngIf="isCreate"><i
              class="fas fa-plus mr-1"></i>新增</button>
          <button class="btn btn-primary mr-2" (click)="openTemplateViewer(templateViewerDialog)"
            *ngIf="currentTab === 1">
            <i class="fas fa-eye mr-1"></i>查看模板
          </button>
        </div>
      </div>
    </div>
  </nb-card-body>

  <!-- Tab 導航 -->
  <nb-card-body class="bg-white pb-0">
    <nb-tabset (changeTab)="onTabChange($event)">
      <nb-tab tabTitle="建案">
        <div class="pt-3">
          <!-- 建案列表 -->
          <div class="col-12 mt-3">
            <div class="table-responsive">
              <table class="table table-striped border " style="min-width: 800px; background-color:#f3f3f3;">
                <thead>
                  <tr style="background-color: #27ae60;" class="d-flex text-white">
                    <th scope="col" class="col-2">建案名稱</th>
                    <th scope="col" class="col-2">工程項目</th>
                    <th scope="col" class="col-1">群組類別</th>
                    <th scope="col" class="col-1">類型</th>
                    <th scope="col" class="col-1">排序</th>
                    <th scope="col" class="col-1">狀態</th>
                    <th scope="col" class="col-1">客變需求顯示</th>
                    <th scope="col" class="col-1">單價</th>
                    <th scope="col" class="col-2">操作功能</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of requirementList; let i = index" class="d-flex">
                    <td class="col-2">{{ data.CBuildCaseName }}</td>
                    <td class="col-2">{{ data.CRequirement }}</td>
                    <td class="col-1">{{ data.CGroupName }}</td>
                    <td class="col-1">{{ getHouseType(data.CHouseType || []) }}</td>
                    <td class="col-1">{{ data.CSort }}</td>
                    <td class="col-1">{{ data.CStatus | getStatusName }}</td>
                    <td class="col-1">{{ getCIsShowText(data) }}</td>
                    <td class="col-1">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>
                    <td class="col-2">
                      <button *ngIf="isUpdate" type="button" class="btn btn-outline-success m-1  btn-sm"
                        (click)="onEdit(data,dialog)"><i class="fas fa-edit mr-1"></i>編輯</button>
                      <button *ngIf="isDelete" type="button" class="btn btn-outline-danger m-1  btn-sm"
                        (click)="onDelete(data)"><i class="far fa-trash-alt mr-1"></i>刪除</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <ngx-pagination [CollectionSize]="totalRecords" [(Page)]="pageIndex" [PageSize]="pageSize"
              (PageChange)="getList()">
            </ngx-pagination>
          </div>
        </div>
      </nb-tab>

      <nb-tab tabTitle="共用">
        <div class="pt-3">
          <!-- 模板列表 -->
          <div class="col-12 mt-3">
            <div class="table-responsive">
              <table class="table table-striped border " style="min-width: 800px; background-color:#f3f3f3;">
                <thead>
                  <tr style="background-color: #27ae60;" class="d-flex text-white">
                    <th scope="col" class="col-2">工程項目</th>
                    <th scope="col" class="col-2">群組類別</th>
                    <th scope="col" class="col-1">類型</th>
                    <th scope="col" class="col-1">排序</th>
                    <th scope="col" class="col-1">狀態</th>
                    <th scope="col" class="col-1">客變需求顯示</th>
                    <th scope="col" class="col-1">單價</th>
                    <th scope="col" class="col-3">操作功能</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of requirementList; let i = index" class="d-flex">
                    <td class="col-2">{{ data.CRequirement }}</td>
                    <td class="col-2">{{ data.CGroupName }}</td>
                    <td class="col-1">{{ getHouseType(data.CHouseType || []) }}</td>
                    <td class="col-1">{{ data.CSort }}</td>
                    <td class="col-1">{{ data.CStatus | getStatusName }}</td>
                    <td class="col-1">{{ getCIsShowText(data) }}</td>
                    <td class="col-1">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>
                    <td class="col-3">
                      <button *ngIf="isUpdate" type="button" class="btn btn-outline-success m-1  btn-sm"
                        (click)="onEdit(data,dialog)"><i class="fas fa-edit mr-1"></i>編輯</button>
                      <button *ngIf="isDelete" type="button" class="btn btn-outline-danger m-1  btn-sm"
                        (click)="onDelete(data)"><i class="far fa-trash-alt mr-1"></i>刪除</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <ngx-pagination [CollectionSize]="totalRecords" [(Page)]="pageIndex" [PageSize]="pageSize"
              (PageChange)="getList()">
            </ngx-pagination>
          </div>
        </div>
      </nb-tab>
    </nb-tabset>
  </nb-card-body>
</nb-card>

<!-- 建案對話框 -->
<ng-template #dialog let-data let-ref="dialogRef">
  <nb-card style="height: 100%; overflow: auto; max-width: 500px;" class="mr-md-5 ml-md-5">
    <nb-card-header>
      <span *ngIf="isNew===true && currentTab === 0">新增建案需求</span>
      <span *ngIf="isNew===false && currentTab === 0">編輯建案需求</span>
      <span *ngIf="isNew===true && currentTab === 1">新增模板需求</span>
      <span *ngIf="isNew===false && currentTab === 1">編輯模板需求</span>
    </nb-card-header>
    <nb-card-body style="padding:1rem 2rem">
      <div class="row">
        <div class="col-12 col-md-12">
          <div class="row">
            <app-form-group [label]="'建案名稱'" [labelFor]="'CBuildCaseID'" [isRequired]="true" *ngIf="currentTab === 0">
              <nb-select class="flex-grow-1" placeholder="請選擇" id="CBuildCaseID" name="CBuildCaseID"
                [(selected)]="saveRequirement.CBuildCaseID">
                <nb-option langg *ngFor="let b of buildCaseList" [value]="b.cID"> {{b.CBuildCaseName}}</nb-option>
              </nb-select>
            </app-form-group>
            <app-form-group [label]="'工程項目'" [labelFor]="'CRequirement'" [isRequired]="true">
              <input type="text" nbInput class="flex-grow-1" id="CRequirement" name="CRequirement" placeholder="工程項目"
                [(ngModel)]="saveRequirement.CRequirement" maxlength="50">
            </app-form-group>
            <app-form-group [label]="'群組類別'" [labelFor]="'CGroupName'" [isRequired]="false">
              <input type="text" nbInput class="flex-grow-1" id="CGroupName" name="CGroupName" placeholder="群組類別"
                [(ngModel)]="saveRequirement.CGroupName" maxlength="20">
            </app-form-group>
            <app-form-group [label]="'排序'" [labelFor]="'CSort'" [isRequired]="true">
              <input type="number" nbInput class="flex-grow-1" id="CSort" name="CSort" placeholder="排序"
                [(ngModel)]="saveRequirement.CSort">
            </app-form-group>
            <app-form-group [label]="'類型'" [labelFor]="'CHouseType'" [isRequired]="true">
              <nb-select class="flex-grow-1" placeholder="請選擇" id="CHouseType" name="CHouseType"
                [(selected)]="saveRequirement.CHouseType" multiple>
                <nb-option langg *ngFor="let type of houseType" [value]="type.value"> {{type.label}}</nb-option>
              </nb-select>
            </app-form-group>
            <app-form-group [label]="'狀態'" [labelFor]="'CStatus'" [isRequired]="true">
              <nb-select class="flex-grow-1" placeholder="請選擇" id="CStatus" name="CStatus"
                [(selected)]="saveRequirement.CStatus">
                <nb-option langg *ngFor="let status of statusOptions" [value]="status.value">
                  {{status.label}}</nb-option>
              </nb-select>
            </app-form-group>
            <app-form-group [label]="'單價'" [labelFor]="'CUnitPrice'" [isRequired]="true">
              <input type="number" nbInput class="flex-grow-1" id="CUnitPrice" name="CUnitPrice" placeholder="單價"
                [(ngModel)]="saveRequirement.CUnitPrice">
            </app-form-group>
            <app-form-group [label]="'單位'" [labelFor]="'CUnit'" [isRequired]="true">
              <input type="text" nbInput class="flex-grow-1" id="CUnit" name="CUnit" placeholder="單位"
                [(ngModel)]="saveRequirement.CUnit">
            </app-form-group>
            <app-form-group [label]="'客變需求顯示'" [labelFor]="'CIsShow'" [isRequired]="false">
              <nb-checkbox id="CIsShow" name="CIsShow" [(ngModel)]="saveRequirement.CIsShow" class="flex-grow-1">
                顯示在客變需求
              </nb-checkbox>
            </app-form-group>
            <app-form-group [label]="'備註說明'" [labelFor]="'CRemark'" [isRequired]="false">
              <textarea nbInput class="flex-grow-1" id="CRemark" name="CRemark" placeholder="備註說明"
                [(ngModel)]="saveRequirement.CRemark" maxlength="100" rows="3"></textarea>
            </app-form-group>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="row">
        <div class="col-12 text-center">
          <button class="btn btn-success mr-2" (click)="save(ref)">確定</button>
          <button class="btn btn-danger mr-2" (click)="ref.close()">取消</button>
        </div>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 模板對話框 -->
<ng-template #templateDialog let-data let-ref="dialogRef">
  <nb-card style="height: 100%; overflow: auto; max-width: 500px;" class="mr-md-5 ml-md-5">
    <nb-card-header>
      <span *ngIf="isNew===true">新增模板需求</span>
      <span *ngIf="isNew===false">編輯模板需求</span>
    </nb-card-header>
    <nb-card-body style="padding:1rem 2rem">
      <div class="row">
        <div class="col-12 col-md-12">
          <div class="row">
            <app-form-group [label]="'工程項目'" [labelFor]="'CRequirement'" [isRequired]="true">
              <input type="text" nbInput class="flex-grow-1" id="CRequirement" name="CRequirement" placeholder="工程項目"
                [(ngModel)]="saveRequirement.CRequirement" maxlength="50">
            </app-form-group>
            <app-form-group [label]="'群組類別'" [labelFor]="'CGroupName'" [isRequired]="false">
              <input type="text" nbInput class="flex-grow-1" id="CGroupName" name="CGroupName" placeholder="群組類別"
                [(ngModel)]="saveRequirement.CGroupName" maxlength="20">
            </app-form-group>
            <app-form-group [label]="'排序'" [labelFor]="'CSort'" [isRequired]="true">
              <input type="number" nbInput class="flex-grow-1" id="CSort" name="CSort" placeholder="排序"
                [(ngModel)]="saveRequirement.CSort">
            </app-form-group>
            <app-form-group [label]="'類型'" [labelFor]="'CHouseType'" [isRequired]="true">
              <nb-select class="flex-grow-1" placeholder="請選擇" id="CHouseType" name="CHouseType"
                [(selected)]="saveRequirement.CHouseType" multiple>
                <nb-option langg *ngFor="let type of houseType" [value]="type.value"> {{type.label}}</nb-option>
              </nb-select>
            </app-form-group>
            <app-form-group [label]="'狀態'" [labelFor]="'CStatus'" [isRequired]="true">
              <nb-select class="flex-grow-1" placeholder="請選擇" id="CStatus" name="CStatus"
                [(selected)]="saveRequirement.CStatus">
                <nb-option langg *ngFor="let status of statusOptions" [value]="status.value">
                  {{status.label}}</nb-option>
              </nb-select>
            </app-form-group>
            <app-form-group [label]="'單價'" [labelFor]="'CUnitPrice'" [isRequired]="true">
              <input type="number" nbInput class="flex-grow-1" id="CUnitPrice" name="CUnitPrice" placeholder="單價"
                [(ngModel)]="saveRequirement.CUnitPrice">
            </app-form-group>
            <app-form-group [label]="'單位'" [labelFor]="'CUnit'" [isRequired]="true">
              <input type="text" nbInput class="flex-grow-1" id="CUnit" name="CUnit" placeholder="單位"
                [(ngModel)]="saveRequirement.CUnit">
            </app-form-group>
            <app-form-group [label]="'客變需求顯示'" [labelFor]="'CIsShow'" [isRequired]="false">
              <nb-checkbox id="CIsShow" name="CIsShow" [(ngModel)]="saveRequirement.CIsShow" class="flex-grow-1">
                顯示在客變需求
              </nb-checkbox>
            </app-form-group>
            <app-form-group [label]="'備註說明'" [labelFor]="'CRemark'" [isRequired]="false">
              <textarea nbInput class="flex-grow-1" id="CRemark" name="CRemark" placeholder="備註說明"
                [(ngModel)]="saveRequirement.CRemark" maxlength="100" rows="3"></textarea>
            </app-form-group>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="row">
        <div class="col-12 text-center">
          <button class="btn btn-success mr-2" (click)="saveTemplate(ref)">確定</button>
          <button class="btn btn-danger mr-2" (click)="ref.close()">取消</button>
        </div>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 共用模板查看元件 Dialog -->
<ng-template #templateViewerDialog let-data let-ref="dialogRef">
  <nb-card style="width: 90vw; max-width: 1200px; height: 80vh;">
    <nb-card-header>
      <h5>模板管理</h5>
    </nb-card-header>
    <nb-card-body style="overflow: auto;">
      <app-template-viewer [templates]="templateList" [templateDetails]="templateDetailList"
        [sharedData]="requirementList" (saveTemplate)="onSaveTemplate($event)"
        (deleteTemplate)="onDeleteTemplate($event)" (selectTemplate)="onSelectTemplate($event)">
      </app-template-viewer>
    </nb-card-body>
    <nb-card-footer>
      <div class="text-center">
        <button class="btn btn-secondary" (click)="ref.close()">關閉</button>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>