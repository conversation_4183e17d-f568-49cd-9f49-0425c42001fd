.template-viewer-modal {
    background: #fff;
    border-radius: 12px;
    padding: 2rem;
    min-width: 500px;
    max-width: 800px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.template-viewer-header {
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;

    h5 {
        color: #333;
        font-weight: 600;
    }
}

// 搜尋容器樣式
.search-container {
    .input-group {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;

        .form-control {
            border: none;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;

            &:focus {
                box-shadow: none;
                border-color: transparent;
            }

            &::placeholder {
                color: #999;
                font-style: italic;
            }
        }

        .input-group-append {
            .btn {
                border: none;
                background: #f8f9fa;
                color: #6c757d;
                padding: 0.75rem 1rem;
                transition: all 0.2s ease;

                &:hover {
                    background: #e9ecef;
                    color: #495057;
                }

                &:focus {
                    box-shadow: none;
                }
            }
        }
    }
}

// 搜尋結果資訊
.search-results-info {
    padding: 0.5rem 0;
    border-left: 3px solid #007bff;
    padding-left: 0.75rem;
    background: #f8f9ff;
    border-radius: 4px;
}

// 表格樣式
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table {
    margin-bottom: 0;

    thead.thead-light {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

        th {
            border: none;
            font-weight: 600;
            color: #495057;
            padding: 1rem 0.75rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }

    tbody {
        tr {
            transition: all 0.2s ease;

            &:hover {
                background-color: #f8f9ff;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            td {
                padding: 1rem 0.75rem;
                border-color: #f0f0f0;
                vertical-align: middle;

                strong {
                    color: #333;
                    font-weight: 600;
                }

                .text-muted {
                    font-size: 0.9rem;
                }
            }
        }
    }
}

// 按鈕組樣式
.btn-group-sm {
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 4px;
        font-weight: 500;
        transition: all 0.2s ease;

        &.btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            border: none;

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
            }
        }

        &.btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
            }
        }

        i {
            margin-right: 0.25rem;
        }
    }
}

// 空狀態樣式
.empty-state {
    padding: 2rem;

    i {
        display: block;
        margin: 0 auto 1rem;
        opacity: 0.5;
    }

    p {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    a {
        color: #007bff;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }
}

// 模板詳情模態框
.template-detail-modal {
    margin-top: 1.5rem;
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.template-detail-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;

    h6 {
        color: #495057;
        font-weight: 600;

        i {
            color: #6c757d;
        }
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}

.template-detail-content {
    padding: 1.5rem;
    max-height: 400px;
    overflow-y: auto;

    .template-description {
        background: #f8f9ff;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;

        strong {
            color: #495057;
        }
    }

    .template-items {
        h6 {
            color: #495057;
            font-weight: 600;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e9ecef;

            i {
                color: #6c757d;
            }
        }
    }
}

.detail-list {
    .detail-item {
        transition: all 0.2s ease;

        &:hover {
            background: #f8f9ff;
            border-radius: 6px;
            margin: 0 -0.5rem;
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }

        &:last-child {
            border-bottom: none;
        }

        .detail-index {
            .badge {
                background: #e9ecef;
                color: #6c757d;
                font-weight: 500;
                padding: 0.375rem 0.5rem;
                border-radius: 50%;
                min-width: 2rem;
                text-align: center;
            }
        }

        .detail-content {
            .detail-field {
                font-size: 0.9rem;
                margin-bottom: 0.25rem;

                strong {
                    color: #495057;
                }
            }

            .detail-value {
                font-size: 0.875rem;
                line-height: 1.4;
                word-break: break-word;
            }
        }
    }
}

// 新增模板表單樣式
.add-template-form {
    .card {
        border: 2px solid #28a745;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(40, 167, 69, 0.1);

        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-bottom: none;
            border-radius: 10px 10px 0 0;

            h6 {
                font-weight: 600;
                margin: 0;

                i {
                    color: rgba(255, 255, 255, 0.9);
                }
            }
        }

        .card-body {
            padding: 1.5rem;
        }
    }

    .form-label {
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;

        .text-danger {
            color: #dc3545 !important;
        }
    }

    .form-control {
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 0.75rem;
        transition: all 0.2s ease;

        &:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        &::placeholder {
            color: #999;
            font-style: italic;
        }
    }

    .available-items-container {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 0.75rem;
        background: #f8f9fa;

        .item-checkbox {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.2s ease;

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background: rgba(40, 167, 69, 0.05);
                border-radius: 4px;
                margin: 0 -0.5rem;
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }

            .form-check-label {
                cursor: pointer;
                width: 100%;
                margin: 0;

                .form-check-input {
                    margin-top: 0.125rem;

                    &:checked {
                        background-color: #28a745;
                        border-color: #28a745;
                    }
                }

                .item-info {
                    flex-grow: 1;

                    .item-name {
                        font-weight: 500;
                        color: #333;
                        margin-bottom: 0.125rem;
                    }

                    small {
                        font-size: 0.8rem;
                        color: #6c757d;
                    }
                }
            }
        }
    }

    .form-actions {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;

        .btn {
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s ease;

            &.btn-secondary {
                background: #6c757d;
                border-color: #6c757d;

                &:hover {
                    background: #5a6268;
                    border-color: #545b62;
                    transform: translateY(-1px);
                }
            }

            &.btn-success {
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                border: none;

                &:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
                }

                &:disabled {
                    opacity: 0.6;
                    transform: none;
                    box-shadow: none;
                }
            }

            i {
                margin-right: 0.25rem;
            }
        }
    }
}