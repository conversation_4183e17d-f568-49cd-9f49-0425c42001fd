<nb-card style="width: 90vw; max-width: 1200px; height: 80vh;">
  <nb-card-header>
    <h5>模板管理</h5>
  </nb-card-header>
  <nb-card-body style="overflow: auto;">
    <div class="template-viewer-modal">
      <div class="template-viewer-header">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <div class="search-section">
            <div class="input-group">
              <input type="text" class="form-control" placeholder="搜尋模板名稱或描述..." [(ngModel)]="searchKeyword"
                (input)="onSearch()" (keyup.enter)="onSearch()">
              <div class="input-group-append">
                <button class="btn btn-outline-secondary" type="button" (click)="onSearch()">
                  <i class="fas fa-search"></i>
                </button>
                <button class="btn btn-outline-secondary" type="button" (click)="clearSearch()" *ngIf="searchKeyword">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>
          <button class="btn btn-success btn-sm" (click)="onAddTemplate()">
            <i class="fas fa-plus mr-1"></i>新增
          </button>
        </div>


      </div>

      <!-- 新增模板表單 -->
      <div *ngIf="showAddForm" class="add-template-form mb-4">
        <div class="card">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="fas fa-plus mr-2"></i>新增模板
            </h6>
          </div>
          <div class="card-body">
            <form (ngSubmit)="saveNewTemplate()">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group mb-3">
                    <label class="form-label">
                      <strong>模板名稱 <span class="text-danger">*</span></strong>
                    </label>
                    <input type="text" class="form-control" [(ngModel)]="newTemplate.name" name="templateName"
                      placeholder="請輸入模板名稱" required maxlength="50">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group mb-3">
                    <label class="form-label">
                      <strong>模板描述</strong>
                    </label>
                    <input type="text" class="form-control" [(ngModel)]="newTemplate.description"
                      name="templateDescription" placeholder="請輸入模板描述（可選）" maxlength="100">
                  </div>
                </div>
              </div>

              <div class="form-group mb-3">
                <label class="form-label">
                  <strong>選擇要加入模板的項目 <span class="text-danger">*</span></strong>
                </label>
                <div class="available-items-container">
                  <div *ngIf="availableData.length === 0" class="text-muted text-center py-3">
                    <i class="fas fa-info-circle mr-1"></i>
                    暫無可選項目
                  </div>
                  <div *ngFor="let item of availableData; let i = index" class="item-checkbox">
                    <label class="form-check-label d-flex align-items-center">
                      <input type="checkbox" class="form-check-input mr-2" [(ngModel)]="item.selected"
                        name="item_{{i}}">
                      <div class="item-info">
                        <div class="item-name">{{ item.CRequirement || item.name }}</div>
                        <small class="text-muted">{{ item.CGroupName || item.description }}</small>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              <div class="form-actions d-flex justify-content-end">
                <button type="button" class="btn btn-secondary mr-2" (click)="cancelAddTemplate()">
                  <i class="fas fa-times mr-1"></i>取消
                </button>
                <button type="submit" class="btn btn-success">
                  <i class="fas fa-save mr-1"></i>儲存模板
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>



      <div class="template-list">
        <!-- 搜尋結果統計 -->
        <div class="search-results-info mb-2" *ngIf="searchKeyword">
          <small class="text-muted">
            找到 {{ filteredTemplates.length }} 個符合「{{ searchKeyword }}」的模板
          </small>
        </div>

        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="thead-light">
              <tr>
                <th width="30%">模板名稱</th>
                <th width="50%">描述</th>
                <th width="20%" class="text-center">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let tpl of filteredTemplates; trackBy: trackByTemplateId">
                <td>
                  <strong>{{ tpl.TemplateName }}</strong>
                </td>
                <td>
                  <span class="text-muted">{{ tpl.Description || '無描述' }}</span>
                </td>
                <td class="text-center">
                  <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-info" (click)="onSelectTemplate(tpl)" title="查看詳情">
                      <i class="fas fa-eye"></i> 查看
                    </button>
                    <button class="btn btn-danger" (click)="tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)"
                      *ngIf="tpl.TemplateID" title="刪除模板">
                      <i class="fas fa-trash"></i> 刪除
                    </button>
                  </div>
                </td>
              </tr>
              <tr *ngIf="!filteredTemplates || filteredTemplates.length === 0">
                <td colspan="3" class="text-center py-4">
                  <div class="empty-state">
                    <i class="fas fa-folder-open fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">
                      {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}
                    </p>
                    <small class="text-muted" *ngIf="searchKeyword">
                      請嘗試其他關鍵字或 <a href="javascript:void(0)" (click)="clearSearch()">清除搜尋</a>
                    </small>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 查看模板細節 -->
      <div *ngIf="selectedTemplate" class="template-detail-modal">
        <div class="template-detail-header d-flex justify-content-between align-items-center">
          <h6 class="mb-0">
            <i class="fas fa-file-alt mr-2"></i>
            模板細節：{{ selectedTemplate!.TemplateName }}
          </h6>
          <button class="btn btn-outline-secondary btn-sm" (click)="closeTemplateDetail()">
            <i class="fas fa-times"></i> 關閉
          </button>
        </div>

        <div class="template-detail-content">
          <div *ngIf="selectedTemplate.Description" class="template-description mb-3">
            <strong>描述：</strong>
            <span class="text-muted">{{ selectedTemplate.Description }}</span>
          </div>

          <div class="template-items">
            <h6 class="mb-2">
              <i class="fas fa-list mr-1"></i>
              模板內容 ({{ currentTemplateDetails.length }} 項)
            </h6>

            <div *ngIf="currentTemplateDetails.length > 0; else noDetails" class="detail-list">
              <div *ngFor="let detail of currentTemplateDetails; let i = index"
                class="detail-item d-flex align-items-center py-2 border-bottom">
                <div class="detail-index">
                  <span class="badge badge-light">{{ i + 1 }}</span>
                </div>
                <div class="detail-content flex-grow-1 ml-2">
                  <div class="detail-field">
                    <strong>{{ detail.FieldName }}:</strong>
                  </div>
                  <div class="detail-value text-muted">
                    {{ detail.FieldValue }}
                  </div>
                </div>
              </div>
            </div>

            <ng-template #noDetails>
              <div class="text-center py-3">
                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                <p class="text-muted mb-0">此模板暫無內容</p>
              </div>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="text-center">
      <button class="btn btn-secondary" (click)="onClose()">
        <i class="fas fa-times mr-1"></i>關閉
      </button>
    </div>
  </nb-card-footer>
</nb-card>